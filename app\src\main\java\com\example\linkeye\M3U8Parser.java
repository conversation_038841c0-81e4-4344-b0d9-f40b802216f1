package com.example.linkeye;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

public class M3U8Parser {
    private static final Pattern EXTINF_PATTERN = Pattern.compile(
        "#EXTINF:-1\\s+(.*)\\s*,(.*)$"
    );

    private static final Pattern TVG_ID_PATTERN = Pattern.compile("tvg-id=\"([^\"]+)\"");
    private static final Pattern TVG_LOGO_PATTERN = Pattern.compile("tvg-logo=\"([^\"]+)\"");
    private static final Pattern GROUP_TITLE_PATTERN = Pattern.compile("group-title=\"([^\"]+)\"");

    private static final String PREFS_NAME = "m3u8_prefs";
    private static final String KEY_M3U8_URL = "m3u8_url";
    private static final String DEFAULT_M3U8_URL = "https://ipvv.nobug.cc/tv/rtsp.m3u8";

    public interface M3U8LoadCallback {
        void onSuccess(List<TvGroup> groups);
        void onError(String error);
    }

    private static final ExecutorService executor = Executors.newCachedThreadPool();
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    public static void loadM3U8Async(Context context, M3U8LoadCallback callback) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        String m3u8Url = prefs.getString(KEY_M3U8_URL, DEFAULT_M3U8_URL);

        executor.execute(() -> {
            try {
                List<TvGroup> groups = parseM3U8FromUrl(m3u8Url);
                mainHandler.post(() -> callback.onSuccess(groups));
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError(e.getMessage()));
            }
        });
    }

    public static List<TvGroup> parseM3U8FromUrl(String urlString) throws Exception {
        Map<String, TvGroup> groupMap = new HashMap<>();
        List<TvGroup> groups = new ArrayList<>();

        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        // If it's HTTPS, disable SSL verification
        if (connection instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConnection = (HttpsURLConnection) connection;
            disableSSLVerification(httpsConnection);
        }

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000); // 10 seconds
        connection.setReadTimeout(15000); // 15 seconds
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Android)");

        try {
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new Exception("HTTP error: " + responseCode);
            }

            InputStream is = connection.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));

            String line;
            String currentChannelInfo = null;
            String currentChannelName = null;
            String currentTvgId = null;
            String currentTvgLogo = null;
            String currentGroupTitle = "未分组";

            while ((line = br.readLine()) != null) {
                line = line.trim();

                if (line.startsWith("#EXTINF:")) {
                    // Parse channel info line
                    Matcher matcher = EXTINF_PATTERN.matcher(line);
                    if (matcher.find()) {
                        currentChannelInfo = matcher.group(1);
                        currentChannelName = matcher.group(2);

                        // Extract tvg-id
                        Matcher tvgIdMatcher = TVG_ID_PATTERN.matcher(currentChannelInfo);
                        currentTvgId = tvgIdMatcher.find() ? tvgIdMatcher.group(1) : "";

                        // Extract tvg-logo
                        Matcher tvgLogoMatcher = TVG_LOGO_PATTERN.matcher(currentChannelInfo);
                        currentTvgLogo = tvgLogoMatcher.find() ? tvgLogoMatcher.group(1) : "";

                        // Extract group-title
                        Matcher groupMatcher = GROUP_TITLE_PATTERN.matcher(currentChannelInfo);
                        currentGroupTitle = groupMatcher.find() ? groupMatcher.group(1) : "未分组";
                    }
                } else if (!line.startsWith("#") && !line.isEmpty() && currentChannelName != null) {
                    // This is a URL line, create channel
                    List<String> sources = parseChannelSources(line);
                    if (!sources.isEmpty()) {
                        TvChannel channel = new TvChannel(
                            currentChannelName,
                            currentTvgId,
                            currentTvgLogo,
                            currentGroupTitle,
                            sources,
                            false // Default not muted for TV channels
                        );

                        // Get or create group
                        TvGroup group = groupMap.get(currentGroupTitle);
                        if (group == null) {
                            group = new TvGroup(currentGroupTitle);
                            groupMap.put(currentGroupTitle, group);
                            groups.add(group);
                        }

                        group.addChannel(channel);
                    }

                    // Reset for next channel
                    currentChannelInfo = null;
                    currentChannelName = null;
                    currentTvgId = null;
                    currentTvgLogo = null;
                    currentGroupTitle = "未分组";
                }
            }

            br.close();
        } finally {
            connection.disconnect();
        }

        return groups;
    }

    private static void disableSSLVerification(HttpsURLConnection connection) {
        try {
            // Create a trust manager that accepts all certificates
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            // Install the all-trusting trust manager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            connection.setSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            connection.setHostnameVerifier(allHostsValid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Fallback method for synchronous loading (for backward compatibility)
    public static List<TvGroup> parseM3U8(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            String m3u8Url = prefs.getString(KEY_M3U8_URL, DEFAULT_M3U8_URL);
            return parseM3U8FromUrl(m3u8Url);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    public static void setM3U8Url(Context context, String url) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putString(KEY_M3U8_URL, url).apply();
    }

    public static String getM3U8Url(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_M3U8_URL, DEFAULT_M3U8_URL);
    }
    
    private static List<String> parseChannelSources(String sourceLine) {
        List<String> sources = new ArrayList<>();
        
        // Split by $ to get multiple sources
        String[] parts = sourceLine.split("\\$");
        for (String part : parts) {
            String url = part.trim();
            if (!url.isEmpty() && (url.startsWith("http") || url.startsWith("rtsp") || url.startsWith("rtmp"))) {
                sources.add(url);
            }
        }
        
        return sources;
    }
}
