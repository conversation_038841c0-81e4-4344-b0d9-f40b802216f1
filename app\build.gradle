plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace "com.example.linkeye"
    compileSdkVersion 35

    defaultConfig {
        applicationId "com.example.linkeye"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
    }
    ndkVersion '27.0.12077973'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
//    implementation(name: 'ijkplayer-cmake-release', ext: 'aar')
}
